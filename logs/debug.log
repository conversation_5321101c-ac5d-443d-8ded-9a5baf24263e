[2m2025-07-28 00:04:40.150[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.220[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.242[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:40.243[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:40.251[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:40.270[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:40.280[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-28 00:04:42.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.208[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.210[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.211[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:42.215[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:42.223[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-28 00:04:44.843[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.845[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.986[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time FROM SYS_MENU WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.989[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:44.990[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:44.993[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String)
[2m2025-07-28 00:04:45.062[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectById [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ?) ORDER BY sort_number ASC
[2m2025-07-28 00:04:45.063[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String)
[2m2025-07-28 00:04:45.129[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 9
[2m2025-07-28 00:04:51.978[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.985[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE SYS_MENU  SET parent_id=?, title=?, path=?, component=?, menu_type=?, sort_number=?,   hide=?  WHERE menu_id=? AND deleted=0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==>  Preparing: UPDATE SYS_MENU SET parent_id = ?, title = ?, path = ?, component = ?, menu_type = ?, sort_number = ?, hide = ? WHERE menu_id = ? AND deleted = 0
[2m2025-07-28 00:04:51.988[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m ==> Parameters: 136D6E63407E740CE0630100007FB121(String), 打印设计测试(String), /bank-note/label-hiprint/label-designer-v2(String), /bank-note/label-hiprint/label-designer-v2(String), 1(Integer), 60(Integer), 0(Integer), e8c7c1c9120f8b26d9a841d6379624d0(String)
[2m2025-07-28 00:04:52.009[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.s.mapper.SysMenuMapper.updateById [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-28 00:04:52.146[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    menu_id,parent_id,title,path,component,menu_type,sort_number,authority,icon,hide,meta,path_name,deleted,create_time,update_time    FROM  SYS_MENU     WHERE deleted=0     AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.161[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?)
[2m2025-07-28 00:04:52.181[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer)
[2m2025-07-28 00:04:52.186[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.m.S.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT menu_id, parent_id, title, path, component, menu_type, sort_number, authority, icon, hide, meta, path_name, deleted, create_time, update_time FROM SYS_MENU WHERE deleted = 0 AND (parent_id = ? AND menu_type = ?) ORDER BY sort_number ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-28 00:04:52.196[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 00000000000000000000000000000000(String), 0(Integer), 10(Long), 0(Long)
[2m2025-07-28 00:04:52.207[0;39m [32mDEBUG[0;39m [35m69539[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.s.mapper.SysMenuMapper.selectList [0;39m [2m:[0;39m <==      Total: 8
