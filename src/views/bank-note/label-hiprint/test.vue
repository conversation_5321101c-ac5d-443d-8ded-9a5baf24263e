<template>
  <div class="test-page">
    <h2>HiPrint 功能测试页面</h2>
    
    <!-- 测试可编辑模板名称 -->
    <div class="test-section">
      <h3>1. 可编辑模板名称测试</h3>
      <EditableTemplateName
        v-model="testTemplateName"
        placeholder="测试模板"
        @change="handleNameChange"
      />
      <p>当前名称: {{ testTemplateName }}</p>
    </div>

    <!-- 测试纸张设置 -->
    <div class="test-section">
      <h3>2. 纸张设置测试</h3>
      <PaperSettings
        :hiprint-template="null"
        @paper-change="handlePaperChange"
        @orientation-change="handleOrientationChange"
      />
      <div v-if="currentPaper">
        <p>当前纸张: {{ currentPaper.name || currentPaper.type }}</p>
        <p>尺寸: {{ currentPaper.width }}×{{ currentPaper.height }}mm</p>
      </div>
    </div>

    <!-- 测试hiprint初始化 -->
    <div class="test-section">
      <h3>3. HiPrint 初始化测试</h3>
      <el-button @click="testHiprintInit" :loading="initLoading">
        初始化 HiPrint
      </el-button>
      <el-button @click="testCreateTemplate" :disabled="!hiprintReady">
        创建模板
      </el-button>
      <el-button @click="testSetPaper" :disabled="!testTemplate">
        设置纸张
      </el-button>
      <p>状态: {{ hiprintStatus }}</p>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-section">
      <h3>4. 测试结果</h3>
      <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import EditableTemplateName from './components/EditableTemplateName.vue';
import PaperSettings from './components/PaperSettings.vue';
import {
  initHiprint,
  createPrintTemplate,
  setPaperSize,
  paperSizes
} from '@/utils/hiprint-config';
import { EleMessage } from 'ele-admin-plus/es';

// 测试数据
const testTemplateName = ref('我的测试模板');
const currentPaper = ref(null);
const initLoading = ref(false);
const hiprintReady = ref(false);
const hiprintStatus = ref('未初始化');
const testTemplate = ref(null);
const testResults = reactive({
  templateName: '',
  paperChanges: [],
  orientationChanges: [],
  hiprintTests: []
});

// 处理名称变化
const handleNameChange = (newName) => {
  testResults.templateName = newName;
  EleMessage.success(`模板名称已更改为: ${newName}`);
};

// 处理纸张变化
const handlePaperChange = (paperInfo) => {
  currentPaper.value = paperInfo;
  testResults.paperChanges.push({
    time: new Date().toLocaleTimeString(),
    paper: { ...paperInfo }
  });
  EleMessage.info(`纸张已更改: ${paperInfo.name || paperInfo.type}`);
};

// 处理方向变化
const handleOrientationChange = (orientation) => {
  testResults.orientationChanges.push({
    time: new Date().toLocaleTimeString(),
    orientation
  });
  EleMessage.info(`方向已更改: ${orientation}`);
};

// 测试hiprint初始化
const testHiprintInit = async () => {
  initLoading.value = true;
  hiprintStatus.value = '初始化中...';
  
  try {
    const hiprint = await initHiprint();
    hiprintReady.value = true;
    hiprintStatus.value = '初始化成功';
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'init',
      success: true,
      result: 'HiPrint 初始化成功'
    });
    EleMessage.success('HiPrint 初始化成功');
  } catch (error) {
    hiprintStatus.value = '初始化失败: ' + error.message;
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'init',
      success: false,
      error: error.message
    });
    EleMessage.error('HiPrint 初始化失败: ' + error.message);
  } finally {
    initLoading.value = false;
  }
};

// 测试创建模板
const testCreateTemplate = () => {
  try {
    testTemplate.value = createPrintTemplate();
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'createTemplate',
      success: true,
      result: '模板创建成功'
    });
    EleMessage.success('模板创建成功');
  } catch (error) {
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'createTemplate',
      success: false,
      error: error.message
    });
    EleMessage.error('模板创建失败: ' + error.message);
  }
};

// 测试设置纸张
const testSetPaper = () => {
  if (!testTemplate.value) {
    EleMessage.error('请先创建模板');
    return;
  }

  try {
    const result = setPaperSize(testTemplate.value, 'A4');
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'setPaper',
      success: result.success,
      result: result
    });
    
    if (result.success) {
      EleMessage.success(`纸张设置成功: ${result.width}×${result.height}mm`);
    } else {
      EleMessage.warning('纸张设置失败: ' + result.error);
    }
  } catch (error) {
    testResults.hiprintTests.push({
      time: new Date().toLocaleTimeString(),
      test: 'setPaper',
      success: false,
      error: error.message
    });
    EleMessage.error('纸张设置失败: ' + error.message);
  }
};
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.test-section p {
  margin: 8px 0;
  color: #606266;
}

pre {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  font-size: 12px;
  line-height: 1.4;
}
</style>
