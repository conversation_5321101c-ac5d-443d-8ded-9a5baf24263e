# 标签模板设计方案对比

## 🎯 两种设计方案对比

### 方案1：单标签设计模式（当前实现）
**文件**: `index.vue`

**特点**:
- 每次设计一个完整的标签模板
- 适合单个标签的精细化设计
- 打印时需要重复渲染多个标签

**优点**:
- 设计简单直观
- 适合复杂标签设计
- 每个标签可以有不同的内容

**缺点**:
- 批量打印效率低
- 纸张利用率不高
- 需要手动处理分页

### 方案2：纸张模板 + 标签循环模式（推荐）
**文件**: `label-designer-v2.vue`

**特点**:
- 设置纸张大小和标签尺寸
- 设计单个标签单元
- 系统自动计算布局和分页

**优点**:
- 纸张利用率高（一张A4可打印多个标签）
- 批量打印效率高
- 自动处理分页和布局
- 更符合实际业务需求

**缺点**:
- 设计复杂度稍高
- 需要考虑标签间距和排版

## 🏗️ 推荐方案架构

### 核心组件

1. **LabelLayoutDesigner.vue** - 布局设计器
   - 纸张大小设置（A4、A5、B4、B5）
   - 标签尺寸设置（大签、小签、自定义）
   - 自动计算布局（列数、行数、利用率）
   - 实时预览效果

2. **BatchPrintPreview.vue** - 批量打印预览
   - 分页预览功能
   - 打印统计信息
   - 页面导航控制

3. **EditableTemplateName.vue** - 可编辑模板名称
   - 点击编辑功能
   - 输入验证
   - 实时保存

4. **PaperSettings.vue** - 纸张设置工具栏
   - 标准纸张规格选择
   - 自定义纸张尺寸
   - 纸张方向切换

## 📊 实际应用场景

### 钱币标签打印场景
- **大签**: 192×26mm，A4纸可打印约6个标签（2列×3行）
- **小签**: 115×21mm，A4纸可打印约12个标签（3列×4行）
- **批量打印**: 100个标签需要17张A4纸（大签）或9张A4纸（小签）

### 布局计算示例
```javascript
// A4纸张: 210×297mm
// 大签尺寸: 192×26mm
// 页边距: 10mm
// 标签间距: 2mm

可用区域: (210-20) × (297-20) = 190×277mm
列数: floor((190+2)/(192+2)) = 0 列 // 标签太宽，需要调整
行数: floor((277+2)/(26+2)) = 9 行

// 调整后的实际布局
实际列数: 1 列
实际行数: 10 行
每页标签数: 10 个
纸张利用率: 约85%
```

## 🚀 使用建议

### 推荐使用方案2的原因：

1. **经济效益**
   - 大幅提高纸张利用率
   - 减少打印成本
   - 符合环保要求

2. **操作效率**
   - 一次设计，批量应用
   - 自动分页处理
   - 减少人工干预

3. **业务匹配**
   - 符合标签打印的实际需求
   - 支持大批量数据处理
   - 便于质量控制

### 实施步骤：

1. **第一阶段**: 使用 `label-designer-v2.vue` 替换现有设计器
2. **第二阶段**: 集成批量数据接口
3. **第三阶段**: 优化打印性能和用户体验

## 🔧 技术实现要点

### 布局计算算法
```javascript
function calculateLayout(paperSize, labelSize, margin, spacing) {
  const usableWidth = paperSize.width - 2 * margin;
  const usableHeight = paperSize.height - 2 * margin;
  
  const cols = Math.floor((usableWidth + spacing) / (labelSize.width + spacing));
  const rows = Math.floor((usableHeight + spacing) / (labelSize.height + spacing));
  
  return {
    cols,
    rows,
    labelsPerPage: cols * rows,
    utilization: calculateUtilization(usableWidth, usableHeight, cols, rows, labelSize, spacing)
  };
}
```

### 批量数据处理
```javascript
function generatePrintPages(data, labelsPerPage) {
  const pages = [];
  for (let i = 0; i < data.length; i += labelsPerPage) {
    pages.push(data.slice(i, i + labelsPerPage));
  }
  return pages;
}
```

## 📝 总结

**强烈推荐使用方案2（纸张模板 + 标签循环）**，因为它：

- ✅ 更符合实际业务需求
- ✅ 提高纸张利用率和打印效率
- ✅ 减少设计和维护成本
- ✅ 支持大规模批量打印
- ✅ 提供更好的用户体验

方案2已经实现了完整的功能，包括布局设计、批量预览、纸张设置等，可以直接投入使用。
