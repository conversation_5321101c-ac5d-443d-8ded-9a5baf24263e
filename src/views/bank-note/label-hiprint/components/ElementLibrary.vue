<template>
  <div class="element-library">
    <div class="library-header">
      <h4>元素库</h4>
      <el-button size="small" @click="refreshFields" :loading="loading">
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>

    <!-- 基础元素 -->
    <div class="element-group">
      <div class="group-header" @click="toggleGroup('basic')">
        <el-icon><CaretRight :class="{ 'expanded': expandedGroups.basic }" /></el-icon>
        <span>基础元素</span>
      </div>
      <el-collapse-transition>
        <div v-show="expandedGroups.basic" class="group-content">
          <div
            v-for="(element, key) in basicElements"
            :key="key"
            class="element-item ep-draggable-item"
            :tid="getHiprintTid(element.template.type)"
            draggable="true"
            @dragstart="handleDragStart($event, 'basic', key, element)"
            @dragend="handleDragEnd"
          >
            <el-icon class="element-icon">
              <component :is="element.icon" />
            </el-icon>
            <span class="element-name">{{ element.name }}</span>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 数据字段 -->
    <div class="element-group">
      <div class="group-header" @click="toggleGroup('fields')">
        <el-icon><CaretRight :class="{ 'expanded': expandedGroups.fields }" /></el-icon>
        <span>数据字段</span>
      </div>
      <el-collapse-transition>
        <div v-show="expandedGroups.fields" class="group-content">
          <div
            v-for="category in fieldCategories"
            :key="category.name"
            class="field-category"
          >
            <div class="category-header" @click="toggleCategory(category.name)">
              <el-icon><CaretRight :class="{ 'expanded': expandedCategories[category.name] }" /></el-icon>
              <span>{{ category.displayName }}</span>
            </div>
            <el-collapse-transition>
              <div v-show="expandedCategories[category.name]" class="category-content">
                <div
                  v-for="field in category.fields"
                  :key="field.fieldName"
                  class="element-item field-item ep-draggable-item"
                  :tid="getHiprintTid('field')"
                  draggable="true"
                  @dragstart="handleDragStart($event, 'field', field.fieldName, field)"
                  @dragend="handleDragEnd"
                  :title="`字段：${field.fieldName}\n类型：${field.fieldType}\n描述：${field.displayName}`"
                >
                  <el-icon class="element-icon">
                    <Document />
                  </el-icon>
                  <span class="element-name">{{ field.displayName }}</span>
                  <span class="field-type">{{ field.fieldType }}</span>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </el-collapse-transition>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {
  CaretRight,
  Refresh,
  Document,
  EditPen,
  Picture,
  Postcard,
  Grid,
  Minus,
  Crop
} from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';
import { getFieldsByCategory } from '../api';
import { elementTemplates } from '@/utils/hiprint-config';

const props = defineProps({
  onElementDrag: {
    type: Function,
    default: () => {}
  }
});

const emit = defineEmits(['element-drag-start', 'element-drag-end']);

// 响应式数据
const loading = ref(false);
const expandedGroups = reactive({
  basic: true,
  fields: true
});
const expandedCategories = reactive({});
const fieldCategories = ref([]);

// 基础元素定义
const basicElements = {
  text: {
    name: '文本',
    icon: EditPen,
    template: elementTemplates.text
  },
  image: {
    name: '图片',
    icon: Picture,
    template: elementTemplates.image
  },
  barcode: {
    name: '条形码',
    icon: Postcard,
    template: elementTemplates.barcode
  },
  qrcode: {
    name: '二维码',
    icon: Grid,
    template: elementTemplates.qrcode
  },
  line: {
    name: '线条',
    icon: Minus,
    template: elementTemplates.line
  },
  rect: {
    name: '矩形',
    icon: Crop,
    template: elementTemplates.rect
  }
};

// 字段分类映射
const categoryMap = {
  'BASIC_INFO': '基本信息',
  'COIN_INFO': '钱币信息',
  'GRADE_INFO': '评级信息',
  'AUDIT_INFO': '审核信息',
  'OTHER': '其他'
};

// 方法定义
const toggleGroup = (groupName) => {
  expandedGroups[groupName] = !expandedGroups[groupName];
};

const toggleCategory = (categoryName) => {
  expandedCategories[categoryName] = !expandedCategories[categoryName];
};

// 获取hiprint默认的tid
const getHiprintTid = (elementType) => {
  // 映射自定义元素类型到hiprint默认类型
  const tidMap = {
    'text': 'defaultModule.text',
    'image': 'defaultModule.image',
    'hline': 'defaultModule.hline',
    'rect': 'defaultModule.rect',
    'field': 'defaultModule.text' // 字段类型使用文本元素
  };

  return tidMap[elementType] || 'defaultModule.text';
};

const handleDragStart = (event, type, key, data) => {
  const dragData = {
    type,
    key,
    data
  };

  event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
  event.dataTransfer.effectAllowed = 'copy';

  emit('element-drag-start', dragData);
};

const handleDragEnd = (event) => {
  emit('element-drag-end');
};

const refreshFields = async () => {
  loading.value = true;
  try {
    const fieldsData = await getFieldsByCategory();

    // 转换数据格式
    fieldCategories.value = Object.keys(fieldsData).map(categoryKey => {
      const fields = fieldsData[categoryKey] || [];
      return {
        name: categoryKey,
        displayName: categoryMap[categoryKey] || categoryKey,
        fields: fields.map(field => ({
          ...field,
          displayName: field.displayName || field.fieldName
        }))
      };
    });

    // 初始化展开状态
    fieldCategories.value.forEach(category => {
      if (expandedCategories[category.name] === undefined) {
        expandedCategories[category.name] = true;
      }
    });

  } catch (error) {
    console.error('加载字段失败:', error);
    EleMessage.error('加载字段失败：' + error.message);
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  refreshFields();
});
</script>

<style scoped>
.element-library {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.library-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.element-group {
  margin-bottom: 16px;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  font-weight: 600;
  color: #555;
  border-bottom: 1px solid #eee;
}

.group-header:hover {
  color: #409eff;
}

.group-header .el-icon {
  margin-right: 8px;
  transition: transform 0.3s;
}

.group-header .el-icon.expanded {
  transform: rotate(90deg);
}

.group-content {
  padding: 8px 0;
}

.element-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;
  user-select: none;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #409eff;
  transform: translateX(2px);
}

.element-item:active {
  cursor: grabbing;
}

.element-icon {
  margin-right: 8px;
  color: #666;
}

.element-name {
  flex: 1;
  font-size: 12px;
  color: #333;
}

.field-category {
  margin-left: 16px;
  margin-bottom: 8px;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.category-header:hover {
  color: #409eff;
}

.category-header .el-icon {
  margin-right: 6px;
  font-size: 12px;
  transition: transform 0.3s;
}

.category-header .el-icon.expanded {
  transform: rotate(90deg);
}

.category-content {
  padding-left: 16px;
}

.field-item {
  position: relative;
}

.field-type {
  font-size: 10px;
  color: #999;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 2px;
  margin-left: 8px;
}

/* 拖拽时的样式 */
.element-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* hiprint 拖拽元素样式 */
.ep-draggable-item {
  position: relative;
}

.ep-draggable-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  pointer-events: none;
}

.ep-draggable-item:hover::after {
  background: rgba(64, 158, 255, 0.1);
  border: 1px dashed #409eff;
}

/* 滚动条样式 */
.element-library::-webkit-scrollbar {
  width: 6px;
}

.element-library::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.element-library::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.element-library::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
