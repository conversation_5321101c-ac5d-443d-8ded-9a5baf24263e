<template>
  <div class="batch-print-preview">
    <el-card shadow="never">
      <template #header>
        <div class="preview-header">
          <h4>批量打印预览</h4>
          <div class="preview-actions">
            <el-button-group size="small">
              <el-button @click="prevPage" :disabled="currentPage <= 1">
                <el-icon><ArrowLeft /></el-icon>
                上一页
              </el-button>
              <el-button @click="nextPage" :disabled="currentPage >= totalPages">
                下一页
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </el-button-group>
            <span class="page-info">
              第 {{ currentPage }} 页，共 {{ totalPages }} 页
            </span>
          </div>
        </div>
      </template>

      <!-- 打印统计信息 -->
      <div class="print-stats">
        <el-descriptions :column="4" size="small" border>
          <el-descriptions-item label="总标签数">
            {{ printData.length }}
          </el-descriptions-item>
          <el-descriptions-item label="每页标签数">
            {{ layoutConfig.layout?.labelsPerPage || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="总页数">
            {{ totalPages }}
          </el-descriptions-item>
          <el-descriptions-item label="纸张规格">
            {{ layoutConfig.paper?.width }}×{{ layoutConfig.paper?.height }}mm
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 预览区域 -->
      <div class="preview-area">
        <div class="page-preview" :style="pagePreviewStyle">
          <!-- 当前页的标签 -->
          <div
            v-for="(item, index) in currentPageData"
            :key="index"
            class="label-item"
            :style="getLabelItemStyle(index)"
          >
            <div class="label-content">
              <!-- 这里渲染实际的标签内容 -->
              <div class="coin-info">
                <div class="coin-name">{{ item.coinName || '钱币名称' }}</div>
                <div class="coin-details">
                  <span>{{ item.serialNumber || 'SN001' }}</span>
                  <span>{{ item.grade || 'MS70' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面导航 -->
      <div class="page-navigation">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="layoutConfig.layout?.labelsPerPage || 1"
          :total="printData.length"
          layout="prev, pager, next, jumper"
          small
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const props = defineProps({
  printData: {
    type: Array,
    default: () => []
  },
  layoutConfig: {
    type: Object,
    default: () => ({})
  },
  templateConfig: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['page-change', 'print-ready']);

// 当前页码
const currentPage = ref(1);

// 计算总页数
const totalPages = computed(() => {
  const labelsPerPage = props.layoutConfig.layout?.labelsPerPage || 1;
  return Math.ceil(props.printData.length / labelsPerPage);
});

// 当前页数据
const currentPageData = computed(() => {
  const labelsPerPage = props.layoutConfig.layout?.labelsPerPage || 1;
  const startIndex = (currentPage.value - 1) * labelsPerPage;
  const endIndex = startIndex + labelsPerPage;
  
  const pageData = props.printData.slice(startIndex, endIndex);
  
  // 如果当前页数据不足，用空对象填充
  while (pageData.length < labelsPerPage) {
    pageData.push({});
  }
  
  return pageData;
});

// 页面预览样式
const pagePreviewStyle = computed(() => {
  if (!props.layoutConfig.paper) return {};
  
  const paper = props.layoutConfig.paper;
  const scale = Math.min(600 / paper.width, 400 / paper.height);
  
  return {
    width: paper.width * scale + 'px',
    height: paper.height * scale + 'px',
    transform: `scale(${scale})`,
    transformOrigin: 'top left'
  };
});

// 获取标签项样式
const getLabelItemStyle = (index) => {
  if (!props.layoutConfig.layout || !props.layoutConfig.labelSize) return {};
  
  const layout = props.layoutConfig.layout;
  const labelSize = props.layoutConfig.labelSize;
  const margin = props.layoutConfig.margin || 10;
  const spacing = props.layoutConfig.spacing || 2;
  
  const col = index % layout.cols;
  const row = Math.floor(index / layout.cols);
  
  const x = margin + col * (labelSize.width + spacing);
  const y = margin + row * (labelSize.height + spacing);
  
  const paper = props.layoutConfig.paper;
  const scale = Math.min(600 / paper.width, 400 / paper.height);
  
  return {
    position: 'absolute',
    left: x * scale + 'px',
    top: y * scale + 'px',
    width: labelSize.width * scale + 'px',
    height: labelSize.height * scale + 'px'
  };
};

// 页面切换
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const handlePageChange = (page) => {
  currentPage.value = page;
  emit('page-change', {
    page,
    data: currentPageData.value,
    totalPages: totalPages.value
  });
};

// 监听数据变化，重置到第一页
watch(() => props.printData.length, () => {
  currentPage.value = 1;
});

// 监听当前页变化
watch(currentPage, (newPage) => {
  emit('page-change', {
    page: newPage,
    data: currentPageData.value,
    totalPages: totalPages.value
  });
});
</script>

<style scoped>
.batch-print-preview {
  height: 100%;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-info {
  font-size: 12px;
  color: #666;
}

.print-stats {
  margin-bottom: 20px;
}

.preview-area {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
  min-height: 500px;
}

.page-preview {
  position: relative;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #ddd;
}

.label-item {
  border: 1px solid #e4e7ed;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.label-content {
  width: 100%;
  height: 100%;
  padding: 2px;
  font-size: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coin-info {
  text-align: center;
}

.coin-name {
  font-weight: bold;
  margin-bottom: 2px;
  font-size: 11px;
  color: #303133;
}

.coin-details {
  display: flex;
  justify-content: space-between;
  font-size: 9px;
  color: #606266;
}

.page-navigation {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 空标签样式 */
.label-item:empty {
  border-style: dashed;
  border-color: #dcdfe6;
  background: #f5f7fa;
}
</style>
