<template>
  <div class="label-layout-designer">
    <!-- 布局设置面板 -->
    <el-card shadow="never" class="layout-settings">
      <template #header>
        <h4>标签布局设置</h4>
      </template>
      
      <el-form :model="layoutConfig" label-width="100px" size="small">
        <!-- 纸张设置 -->
        <el-form-item label="纸张大小">
          <el-select v-model="layoutConfig.paperType" @change="handlePaperChange">
            <el-option
              v-for="paper in paperOptions"
              :key="paper.type"
              :label="paper.name"
              :value="paper.type"
            />
          </el-select>
          <span class="paper-info">
            ({{ currentPaper.width }}×{{ currentPaper.height }}mm)
          </span>
        </el-form-item>

        <!-- 纸张方向 -->
        <el-form-item label="纸张方向">
          <el-radio-group v-model="layoutConfig.orientation" @change="handleOrientationChange">
            <el-radio value="portrait">纵向</el-radio>
            <el-radio value="landscape">横向</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 标签尺寸 -->
        <el-form-item label="标签尺寸">
          <el-select v-model="layoutConfig.labelType" @change="handleLabelTypeChange">
            <el-option label="大签 (192×26mm)" value="large" />
            <el-option label="小签 (115×21mm)" value="small" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <!-- 自定义标签尺寸 -->
        <template v-if="layoutConfig.labelType === 'custom'">
          <el-form-item label="标签宽度">
            <el-input-number
              v-model="layoutConfig.labelWidth"
              :min="50"
              :max="500"
              :step="1"
              @change="calculateLayout"
            />
            <span class="unit">mm</span>
          </el-form-item>
          <el-form-item label="标签高度">
            <el-input-number
              v-model="layoutConfig.labelHeight"
              :min="10"
              :max="100"
              :step="1"
              @change="calculateLayout"
            />
            <span class="unit">mm</span>
          </el-form-item>
        </template>

        <!-- 间距设置 -->
        <el-form-item label="标签间距">
          <el-input-number
            v-model="layoutConfig.spacing"
            :min="0"
            :max="20"
            :step="0.5"
            :precision="1"
            @change="calculateLayout"
          />
          <span class="unit">mm</span>
        </el-form-item>

        <!-- 页边距 -->
        <el-form-item label="页边距">
          <el-input-number
            v-model="layoutConfig.margin"
            :min="5"
            :max="50"
            :step="1"
            @change="calculateLayout"
          />
          <span class="unit">mm</span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 布局预览 -->
    <el-card shadow="never" class="layout-preview">
      <template #header>
        <div class="preview-header">
          <h4>布局预览</h4>
          <div class="layout-info">
            <span>{{ layoutResult.cols }}列 × {{ layoutResult.rows }}行</span>
            <span>每页 {{ layoutResult.labelsPerPage }} 个标签</span>
          </div>
        </div>
      </template>

      <div class="preview-container">
        <div
          class="paper-preview"
          :style="paperPreviewStyle"
        >
          <div
            v-for="(label, index) in previewLabels"
            :key="index"
            class="label-preview"
            :style="getLabelStyle(index)"
            :class="{ 'active': selectedLabelIndex === index }"
            @click="selectLabel(index)"
          >
            <div class="label-content">
              标签 {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>

      <!-- 布局统计信息 -->
      <div class="layout-stats">
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="纸张利用率">
            {{ layoutResult.utilization }}%
          </el-descriptions-item>
          <el-descriptions-item label="可用区域">
            {{ layoutResult.usableWidth }}×{{ layoutResult.usableHeight }}mm
          </el-descriptions-item>
          <el-descriptions-item label="浪费空间">
            {{ layoutResult.wastedSpace }}mm²
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'layout-change']);

// 纸张规格定义
const paperOptions = [
  { type: 'A4', name: 'A4', width: 210, height: 297 },
  { type: 'A5', name: 'A5', width: 148, height: 210 },
  { type: 'B4', name: 'B4', width: 250, height: 353 },
  { type: 'B5', name: 'B5', width: 176, height: 250 }
];

// 标签规格定义
const labelSizes = {
  large: { width: 192, height: 26 },
  small: { width: 115, height: 21 },
  custom: { width: 100, height: 30 }
};

// 布局配置
const layoutConfig = reactive({
  paperType: 'A4',
  orientation: 'portrait',
  labelType: 'large',
  labelWidth: 192,
  labelHeight: 26,
  spacing: 2,
  margin: 10
});

// 当前纸张信息
const currentPaper = computed(() => {
  const paper = paperOptions.find(p => p.type === layoutConfig.paperType);
  if (layoutConfig.orientation === 'landscape') {
    return { width: paper.height, height: paper.width };
  }
  return paper;
});

// 当前标签尺寸
const currentLabelSize = computed(() => {
  if (layoutConfig.labelType === 'custom') {
    return { width: layoutConfig.labelWidth, height: layoutConfig.labelHeight };
  }
  return labelSizes[layoutConfig.labelType];
});

// 布局计算结果
const layoutResult = reactive({
  cols: 0,
  rows: 0,
  labelsPerPage: 0,
  utilization: 0,
  usableWidth: 0,
  usableHeight: 0,
  wastedSpace: 0
});

// 预览相关
const selectedLabelIndex = ref(0);
const previewLabels = computed(() => {
  return Array.from({ length: layoutResult.labelsPerPage }, (_, index) => ({
    index,
    x: (index % layoutResult.cols) * (currentLabelSize.value.width + layoutConfig.spacing),
    y: Math.floor(index / layoutResult.cols) * (currentLabelSize.value.height + layoutConfig.spacing)
  }));
});

// 纸张预览样式
const paperPreviewStyle = computed(() => {
  const scale = Math.min(400 / currentPaper.value.width, 300 / currentPaper.value.height);
  return {
    width: currentPaper.value.width * scale + 'px',
    height: currentPaper.value.height * scale + 'px',
    transform: `scale(${scale})`,
    transformOrigin: 'top left'
  };
});

// 计算布局
const calculateLayout = () => {
  const paper = currentPaper.value;
  const label = currentLabelSize.value;
  const margin = layoutConfig.margin;
  const spacing = layoutConfig.spacing;

  // 可用区域
  const usableWidth = paper.width - 2 * margin;
  const usableHeight = paper.height - 2 * margin;

  // 计算列数和行数
  const cols = Math.floor((usableWidth + spacing) / (label.width + spacing));
  const rows = Math.floor((usableHeight + spacing) / (label.height + spacing));

  // 每页标签数
  const labelsPerPage = cols * rows;

  // 实际使用的区域
  const usedWidth = cols * label.width + (cols - 1) * spacing;
  const usedHeight = rows * label.height + (rows - 1) * spacing;

  // 利用率计算
  const totalUsableArea = usableWidth * usableHeight;
  const usedArea = usedWidth * usedHeight;
  const utilization = totalUsableArea > 0 ? Math.round((usedArea / totalUsableArea) * 100) : 0;

  // 浪费空间
  const wastedSpace = Math.round(totalUsableArea - usedArea);

  // 更新结果
  Object.assign(layoutResult, {
    cols,
    rows,
    labelsPerPage,
    utilization,
    usableWidth: Math.round(usableWidth),
    usableHeight: Math.round(usableHeight),
    wastedSpace
  });

  // 触发事件
  const layoutData = {
    ...layoutConfig,
    paper: currentPaper.value,
    labelSize: currentLabelSize.value,
    layout: { ...layoutResult }
  };
  
  emit('update:modelValue', layoutData);
  emit('layout-change', layoutData);
};

// 获取标签样式
const getLabelStyle = (index) => {
  const label = previewLabels.value[index];
  const scale = Math.min(400 / currentPaper.value.width, 300 / currentPaper.value.height);
  
  return {
    left: (layoutConfig.margin + label.x) * scale + 'px',
    top: (layoutConfig.margin + label.y) * scale + 'px',
    width: currentLabelSize.value.width * scale + 'px',
    height: currentLabelSize.value.height * scale + 'px'
  };
};

// 事件处理
const handlePaperChange = () => {
  calculateLayout();
};

const handleOrientationChange = () => {
  calculateLayout();
};

const handleLabelTypeChange = () => {
  if (layoutConfig.labelType !== 'custom') {
    const size = labelSizes[layoutConfig.labelType];
    layoutConfig.labelWidth = size.width;
    layoutConfig.labelHeight = size.height;
  }
  calculateLayout();
};

const selectLabel = (index) => {
  selectedLabelIndex.value = index;
};

// 监听配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(layoutConfig, newValue);
  }
}, { immediate: true });

// 初始化
onMounted(() => {
  calculateLayout();
});
</script>

<style scoped>
.label-layout-designer {
  display: flex;
  gap: 20px;
  height: 100%;
}

.layout-settings {
  width: 350px;
  flex-shrink: 0;
}

.layout-preview {
  flex: 1;
}

.paper-info {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

.unit {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.layout-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
  min-height: 400px;
}

.paper-preview {
  position: relative;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
}

.label-preview {
  position: absolute;
  border: 1px dashed #409eff;
  background: rgba(64, 158, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.label-preview:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409eff;
}

.label-preview.active {
  background: rgba(64, 158, 255, 0.3);
  border: 2px solid #409eff;
}

.label-content {
  font-size: 10px;
  color: #409eff;
  text-align: center;
}

.layout-stats {
  margin-top: 16px;
}
</style>
