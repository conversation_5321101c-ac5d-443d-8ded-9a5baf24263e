/**
 * hiprint 配置工具
 * 用于统一管理 hiprint 的初始化和配置
 */

let hiprintInstance = null;
let isInitialized = false;
let defaultProvider = null;

/**
 * 初始化 hiprint
 * @param {Object} options 配置选项
 * @returns {Promise} 返回 hiprint 实例
 */
export async function initHiprint(options = {}) {
  try {
    // 如果已经初始化，直接返回实例
    if (isInitialized && hiprintInstance) {
      return hiprintInstance;
    }

    // 动态导入 hiprint
    const { hiprint, defaultElementTypeProvider } = await import('vue-plugin-hiprint');

    // 默认配置
    const defaultConfig = {
      host: '', // 禁用WebSocket连接，避免连接错误
      token: '',
      debug: false,
      ...options
    };

    // 创建默认provider
    defaultProvider = new defaultElementTypeProvider();

    // 初始化 hiprint（先不注册自定义provider，使用默认的）
    hiprint.init({
      ...defaultConfig,
      providers: [defaultProvider]
    });

    hiprintInstance = hiprint;
    isInitialized = true;

    console.log('hiprint 初始化成功');
    return hiprint;

  } catch (error) {
    console.error('hiprint 初始化失败:', error);
    throw new Error('hiprint 插件加载失败：' + error.message);
  }
}

/**
 * 获取元素模板配置
 * @param {string} tid 元素类型ID
 * @returns {Object|null} 元素模板
 */
export function getElementTemplate(tid) {
  return elementTemplates[tid] || null;
}

/**
 * 获取 hiprint 实例
 * @returns {Object|null} hiprint 实例
 */
export function getHiprintInstance() {
  return hiprintInstance;
}

/**
 * 获取默认provider
 * @returns {Object|null} provider实例
 */
export function getDefaultProvider() {
  return defaultProvider;
}

/**
 * 检查 hiprint 是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isHiprintInitialized() {
  return isInitialized;
}

/**
 * 创建打印模板
 * @param {Object} templateData 模板数据
 * @returns {Object} 模板实例
 */
export function createPrintTemplate(templateData = null) {
  if (!hiprintInstance) {
    throw new Error('hiprint 未初始化，请先调用 initHiprint()');
  }

  let template;
  if (templateData) {
    template = new hiprintInstance.PrintTemplate(templateData);
  } else {
    template = new hiprintInstance.PrintTemplate();
  }

  return template;
}

/**
 * 设计器配置
 */
export const designerConfig = {
  grid: true,
  gridColor: '#ddd',
  gridSize: 5,
  showRuler: true,
  rulerColor: '#999',
  backgroundColor: '#f5f5f5'
};

/**
 * 预览配置
 */
export const previewConfig = {
  preview: true,
  width: '100%',
  height: 'auto'
};

/**
 * 打印配置
 */
export const printConfig = {
  margin: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10
  },
  pageSize: 'A4',
  orientation: 'portrait' // portrait | landscape
};

/**
 * 纸张规格定义 (单位: mm)
 */
export const paperSizes = {
  A3: { width: 297, height: 420, name: 'A3' },
  A4: { width: 210, height: 297, name: 'A4' },
  A5: { width: 148, height: 210, name: 'A5' },
  B3: { width: 353, height: 500, name: 'B3' },
  B4: { width: 250, height: 353, name: 'B4' },
  B5: { width: 176, height: 250, name: 'B5' }
};

/**
 * 设置模板纸张大小
 * @param {Object} template hiprint模板实例
 * @param {string|Object} paperType 纸张类型或自定义尺寸对象
 * @param {number} customWidth 自定义宽度(mm)
 * @param {number} customHeight 自定义高度(mm)
 */
export function setPaperSize(template, paperType, customWidth = null, customHeight = null) {
  if (!template) {
    throw new Error('模板实例不存在');
  }

  let width, height;

  if (typeof paperType === 'string' && paperSizes[paperType]) {
    // 使用预定义纸张规格
    const paper = paperSizes[paperType];
    width = paper.width;
    height = paper.height;
  } else if (typeof paperType === 'object' && paperType.width && paperType.height) {
    // 使用传入的纸张对象
    width = paperType.width;
    height = paperType.height;
  } else if (customWidth && customHeight) {
    // 使用自定义尺寸
    width = customWidth;
    height = customHeight;
  } else {
    throw new Error('无效的纸张规格参数');
  }

  // 应用纸张设置
  if (typeof template.setPaper === 'function') {
    template.setPaper(width, height);
    console.log(`设置纸张大小: ${width}×${height}mm`);
    return { width, height, success: true };
  } else {
    console.warn('模板不支持setPaper方法');
    return { width, height, success: false, error: '模板不支持setPaper方法' };
  }
}

/**
 * 常用元素模板 - 基于 hiprint 标准格式
 */
export const elementTemplates = {
  text: {
    tid: 'text',
    title: '文本',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 30,
      width: 200,
      title: '文本',
      text: '文本内容',
      fontSize: 14,
      fontFamily: 'Microsoft YaHei',
      color: '#000000',
      textAlign: 'left',
      lineHeight: 1.2,
      backgroundColor: '#ffffff',
      borderWidth: 1,
      borderStyle: 'solid',
      borderColor: '#cccccc'
    }
  },
  image: {
    tid: 'image',
    title: '图片',
    type: 'image',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '图片',
      src: '',
      fit: 'contain'
    }
  },
  barcode: {
    tid: 'barcode',
    title: '条形码',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 50,
      width: 200,
      title: '条形码',
      text: '123456789',
      textType: 'barcode',
      code: 'CODE128',
      fontSize: 12
    }
  },
  qrcode: {
    tid: 'qrcode',
    title: '二维码',
    type: 'text',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 100,
      title: '二维码',
      text: 'https://example.com',
      textType: 'qrcode'
    }
  },
  line: {
    tid: 'line',
    title: '线条',
    type: 'hline',
    options: {
      left: 50,
      top: 50,
      height: 1,
      width: 200,
      title: '线条'
    }
  },
  rect: {
    tid: 'rect',
    title: '矩形',
    type: 'rect',
    options: {
      left: 50,
      top: 50,
      height: 100,
      width: 200,
      title: '矩形'
    }
  }
};

/**
 * 字段类型映射
 */
export const fieldTypeMap = {
  'String': 'text',
  'Integer': 'text',
  'Long': 'text',
  'Double': 'text',
  'BigDecimal': 'text',
  'Date': 'text',
  'LocalDateTime': 'text',
  'Boolean': 'text'
};

/**
 * 获取字段对应的元素模板
 * @param {string} fieldType 字段类型
 * @param {string} fieldName 字段名称
 * @param {string} displayName 显示名称
 * @returns {Object} 元素模板
 */
export function getFieldElementTemplate(fieldType, fieldName, displayName) {
  const elementType = fieldTypeMap[fieldType] || 'text';
  const template = { ...elementTemplates[elementType] };

  // 设置字段相关属性
  template.options.title = displayName || fieldName;
  template.options.field = fieldName;
  template.options.fieldType = fieldType;

  return template;
}

/**
 * 错误处理
 */
export function handleHiprintError(error) {
  console.error('hiprint 错误:', error);

  // 根据错误类型提供不同的处理建议
  if (error.message.includes('WebSocket')) {
    console.warn('WebSocket 连接失败，这是正常的，不影响设计器功能');
    return '打印服务连接失败，但设计器功能正常';
  } else if (error.message.includes('PrintTemplate')) {
    return '模板创建失败，请检查模板数据格式';
  } else if (error.message.includes('design')) {
    return '设计器初始化失败，请刷新页面重试';
  } else {
    return '未知错误：' + error.message;
  }
}

/**
 * 检测 hiprint API
 * @returns {Object} API 信息
 */
export function detectHiprintAPI() {
  if (!hiprintInstance) {
    return { error: 'hiprint 未初始化' };
  }

  const template = new hiprintInstance.PrintTemplate();
  const apiInfo = {
    templateMethods: Object.getOwnPropertyNames(template),
    templatePrototype: Object.getOwnPropertyNames(Object.getPrototypeOf(template)),
    hasAddPrintElement: typeof template.addPrintElement === 'function',
    hasAddElement: typeof template.addElement === 'function',
    hasAddPrintPanel: typeof template.addPrintPanel === 'function',
    hasDesign: typeof template.design === 'function',
    hasGetJson: typeof template.getJson === 'function',
    panels: template.panels || null
  };

  console.log('hiprint API 检测结果:', apiInfo);
  return apiInfo;
}

/**
 * 构建可拖拽元素
 * @param {string} containerId 容器ID
 */
export function buildDraggableElements(containerId = '.ep-draggable-item') {
  if (!hiprintInstance) {
    console.warn('hiprint未初始化，无法构建拖拽元素');
    return;
  }

  try {
    // 等待DOM元素完全渲染
    setTimeout(() => {
      const elements = document.querySelectorAll(containerId);
      console.log(`找到 ${elements.length} 个拖拽元素`);

      if (elements.length > 0) {
        // 使用jQuery选择器构建拖拽元素
        if (window.$ && window.$(containerId).length > 0) {
          hiprintInstance.PrintElementTypeManager.buildByHtml(window.$(containerId));
          console.log('拖拽元素构建成功');
        } else {
          console.warn('jQuery未加载或元素未找到');
        }
      } else {
        console.warn('未找到拖拽元素，请检查ElementLibrary组件是否正确渲染');
      }
    }, 100);
  } catch (error) {
    console.error('构建拖拽元素失败:', error);
  }
}

/**
 * 安全添加元素到模板
 * @param {Object} template 模板实例
 * @param {Object} elementConfig 元素配置
 * @returns {Object} 添加结果
 */
export function safeAddElement(template, elementConfig) {
  console.log('开始添加元素:', elementConfig);

  try {
    // 确保有面板存在
    let panel = null;

    // 检查是否已有面板
    if (template.panels && Array.isArray(template.panels) && template.panels.length > 0) {
      panel = template.panels[0];
      console.log('使用现有面板:', panel);
    } else {
      // 创建新面板 - 这是 hiprint 的标准做法
      if (typeof template.addPrintPanel === 'function') {
        panel = template.addPrintPanel();
        console.log('创建新面板:', panel);
      }
    }

    if (!panel) {
      return { success: false, error: '无法创建或获取面板' };
    }

    // 确保元素配置有正确的类型和唯一ID
    if (!elementConfig.tid) {
      elementConfig.tid = 'elem_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    }

    // 完善元素基本属性
    if (!elementConfig.options) {
      elementConfig.options = {};
    }

    // 设置一些默认属性，确保元素可见
    if (!elementConfig.options.backgroundColor) {
      elementConfig.options.backgroundColor = '#ffffff';
    }

    if (!elementConfig.options.borderWidth) {
      elementConfig.options.borderWidth = 1;
    }

    if (!elementConfig.options.borderColor) {
      elementConfig.options.borderColor = '#000000';
    }

    if (!elementConfig.options.borderStyle) {
      elementConfig.options.borderStyle = 'solid';
    }

    // 确保位置属性
    if (typeof elementConfig.options.left !== 'number') {
      elementConfig.options.left = 50;
    }

    if (typeof elementConfig.options.top !== 'number') {
      elementConfig.options.top = 50;
    }

    // 根据元素类型使用不同的添加方法
    const elementType = elementConfig.type || elementConfig.tid || 'text';
    console.log('元素类型:', elementType);

    let result = null;
    let addMethod = null;

    // 直接使用面板原始API添加元素（绕过封装的方法）
    if (typeof panel.printElements === 'object' && Array.isArray(panel.printElements) &&
        panel.createPrintElement && typeof panel.createPrintElement === 'function') {
      try {
        // 使用原始API直接创建并添加元素
        console.log('尝试使用原始API添加元素');
        const printElement = panel.createPrintElement(elementConfig);
        if (printElement) {
          panel.printElements.push(printElement);
          result = printElement;
          addMethod = 'panel.printElements.push';
          console.log('使用原始API添加元素成功:', result);
        }
      } catch (error) {
        console.error('使用原始API添加元素失败:', error);
      }
    }

    // 如果直接添加失败，尝试使用特定类型的添加方法
    if (!result) {
      switch (elementType) {
        case 'text':
          if (typeof panel.addPrintText === 'function') {
            try {
              // 确保文本元素有必要的属性
              if (!elementConfig.options.text) elementConfig.options.text = '文本内容';

              result = panel.addPrintText(elementConfig);
              addMethod = 'panel.addPrintText';
              console.log('使用 addPrintText 成功:', result);
            } catch (error) {
              console.error('addPrintText 执行失败:', error);
            }
          }
          break;

        case 'image':
          if (typeof panel.addPrintImage === 'function') {
            try {
              result = panel.addPrintImage(elementConfig);
              addMethod = 'panel.addPrintImage';
              console.log('使用 addPrintImage 成功:', result);
            } catch (error) {
              console.error('addPrintImage 执行失败:', error);
            }
          }
          break;

        case 'hline':
        case 'line':
          if (typeof panel.addPrintHline === 'function') {
            try {
              result = panel.addPrintHline(elementConfig);
              addMethod = 'panel.addPrintHline';
              console.log('使用 addPrintHline 成功:', result);
            } catch (error) {
              console.error('addPrintHline 执行失败:', error);
            }
          }
          break;

        case 'rect':
          if (typeof panel.addPrintRect === 'function') {
            try {
              result = panel.addPrintRect(elementConfig);
              addMethod = 'panel.addPrintRect';
              console.log('使用 addPrintRect 成功:', result);
            } catch (error) {
              console.error('addPrintRect 执行失败:', error);
            }
          }
          break;

        case 'field':
          // 处理数据字段元素
          if (typeof panel.addPrintText === 'function') {
            try {
              // 确保有字段名称
              if (!elementConfig.options.field && elementConfig.key) {
                elementConfig.options.field = elementConfig.key;
                elementConfig.options.title = elementConfig.key;
              }

              result = panel.addPrintText(elementConfig);
              addMethod = 'panel.addPrintText(field)';
              console.log('使用 addPrintText 添加字段成功:', result);
            } catch (error) {
              console.error('添加字段执行失败:', error);
            }
          }
          break;
      }
    }

    // 如果特定方法不存在或失败，尝试通用方法
    if (!result && typeof panel.addPrintElement === 'function') {
      try {
        result = panel.addPrintElement(elementConfig);
        addMethod = 'panel.addPrintElement';
        console.log('使用 addPrintElement 成功:', result);
      } catch (error) {
        console.error('addPrintElement 执行失败:', error);
      }
    }

    if (!result && typeof panel.addElement === 'function') {
      try {
        result = panel.addElement(elementConfig);
        addMethod = 'panel.addElement';
        console.log('使用 addElement 成功:', result);
      } catch (error) {
        console.error('addElement 执行失败:', error);
      }
    }

    // 最后尝试直接在模板上添加
    if (!result && typeof template.addPrintElement === 'function') {
      try {
        result = template.addPrintElement(elementConfig);
        addMethod = 'template.addPrintElement';
        console.log('使用 template.addPrintElement 成功:', result);
      } catch (error) {
        console.error('template.addPrintElement 执行失败:', error);
      }
    }

    // 如果成功添加了元素
    if (result !== null && addMethod) {
      // 检查并更新DOM
      try {
        // 如果有DOM操作方法，直接添加元素到DOM
        if (result && typeof result.designTarget === 'function') {
          const $target = result.designTarget();
          if ($target) {
            console.log('元素已有设计目标');
          } else if (result.createDesignTarget && typeof result.createDesignTarget === 'function') {
            console.log('尝试创建设计目标');
            result.createDesignTarget();
          }
        }

        // 尝试强制刷新面板显示
        if (panel.hideDesignOptions) {
          panel.hideDesignOptions();
        }

        if (panel.updatePrintElements && typeof panel.updatePrintElements === 'function') {
          console.log('尝试更新面板元素');
          panel.updatePrintElements();
        }

        // 尝试刷新设计器显示
        if (typeof template.setPaper === 'function') {
          // 重新设置纸张大小可以强制重绘
          const paperType = template.getPaperType ? template.getPaperType() : 'A4';
          template.setPaper(paperType);
          console.log('重新设置纸张类型触发刷新:', paperType);
        }

        // 手动调用重绘方法
        if (typeof template.refresh === 'function') {
          template.refresh();
          console.log('手动调用模板刷新方法');
        }

        // 检查面板元素
        setTimeout(() => {
          try {
            const elements = panel.printElements || panel.elements || [];
            const elementCount = Array.isArray(elements) ? elements.length : 0;
            console.log('面板元素数量:', elementCount);
            console.log('面板对象:', panel);
            console.log('模板面板数量:', template.panels ? template.panels.length : 0);

            if (elementCount === 0) {
              console.warn('警告: 面板元素数组为空，可能需要重新加载设计器');
            } else {
              console.log('元素列表:', elements);
            }
          } catch (e) {
            console.error('检查面板元素时出错:', e);
          }
        }, 300);
      } catch (verifyError) {
        console.warn('验证元素添加时出错:', verifyError);
      }

      return { success: true, method: addMethod, result };
    }

    return {
      success: false,
      error: '未找到可用的添加元素方法',
      debug: {
        elementType,
        panelMethods: panel ? Object.getOwnPropertyNames(panel) : null,
        templateMethods: Object.getOwnPropertyNames(template),
        panelExists: !!panel,
        panelType: typeof panel
      }
    };

  } catch (error) {
    console.error('添加元素时发生错误:', error);
    return { success: false, error: error.message, stack: error.stack };
  }
}

/**
 * 重置 hiprint 状态
 */
export function resetHiprint() {
  hiprintInstance = null;
  isInitialized = false;
}

export default {
  initHiprint,
  getHiprintInstance,
  isHiprintInitialized,
  createPrintTemplate,
  detectHiprintAPI,
  safeAddElement,
  designerConfig,
  previewConfig,
  printConfig,
  paperSizes,
  setPaperSize,
  elementTemplates,
  fieldTypeMap,
  getFieldElementTemplate,
  handleHiprintError,
  resetHiprint
};
